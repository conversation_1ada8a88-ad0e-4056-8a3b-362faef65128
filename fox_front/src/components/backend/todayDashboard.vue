<template>
<div class="dashboard">
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>总用户数量</span>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.totalUserCount }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日新增用户</span>
            <el-icon><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.todayNewUserCount }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日订单数</span>
            <el-icon><List /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.todayOrderCount }}</span>
          <span class="label">单</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日未支付订单</span>
            <el-icon><Warning /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.noPayTodayOrderCount }}</span>
          <span class="label">单</span>
        </div>
      </el-card>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日收入</span>
            <el-icon><Money /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.todayIncome }}</span>
          <span class="label">元</span>
        </div>
      </el-card>
    </el-col>

  </el-row>

  <!-- 使用统计 -->
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>在线用户数量</span>
            <el-icon><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.onlineUserCount }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日活跃用户</span>
            <el-icon><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.activeUsers }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>总使用次数</span>
            <el-icon><ChatDotRound /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.totalUsage }}</span>
          <span class="label">次</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>人均使用次数</span>
            <el-icon><DataAnalysis /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.avgUsagePerUser }}</span>
          <span class="label">次/人</span>
        </div>
      </el-card>
    </el-col>
  </el-row>

  <!-- 模型使用统计 -->
  <el-row :gutter="20" v-if="usageStats.modelStats && usageStats.modelStats.length > 0">
    <el-col :span="24">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>模型使用统计</span>
            <el-icon><PieChart /></el-icon>
          </div>
        </template>
        <div class="model-stats">
          <div v-for="(model, index) in usageStats.modelStats" :key="index" class="model-stat-item">
            <span class="model-name">{{ model.model }}</span>
            <el-progress 
              :percentage="Math.round((model.count / usageStats.totalModelUsage) * 100)"
              :format="() => model.count + '次'"
              :stroke-width="15"
              :color="getProgressColor(index)"
            />
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import api from '@/axios'
import { User, Plus, List, Warning, Money, ChatDotRound, DataAnalysis, PieChart } from '@element-plus/icons-vue'

const summary = ref({})
const usageStats = ref({
  activeUsers: 0,
  totalUsage: 0,
  avgUsagePerUser: '0.00',
  modelStats: [],
  totalModelUsage: 0
})

const getSummary = async () => {
  try {
    const res = await api.get('/api/dashboard/summary')
    if (res.data.code === 0) {
      const data = res.data.data
      summary.value = data
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

const getUsageStats = async () => {
  try {
    const res = await api.get('/api/dashboard/today-usage')
    if (res.data.code === 0) {
      usageStats.value = res.data.data
    }
  } catch (error) {
    console.error('获取使用统计失败:', error)
  }
}

// 获取进度条颜色
const getProgressColor = (index) => {
  const colors = [
    '#409EFF',
    '#67C23A',
    '#E6A23C',
    '#F56C6C',
    '#909399',
    '#36D1DC',
    '#FF6B6B',
    '#4ECDC4'
  ]
  return colors[index % colors.length]
}

onMounted(() => {
  getSummary()
  getUsageStats()
})
</script>

<style scoped>
.dashboard {
  padding: 12px;
}

.box-card {
  margin-bottom: 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.box-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}

.card-header span {
  font-size: 13px;
  font-weight: 600;
  color: #606266;
}

.card-header .el-icon {
  font-size: 14px;
  color: #409EFF;
}

.card-content {
  text-align: center;
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.number {
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(45deg, #409EFF, #36D1DC);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.1;
}

.label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.el-col {
  animation: fadeInUp 0.5s ease forwards;
}

.el-col:nth-child(1) { animation-delay: 0.1s; }
.el-col:nth-child(2) { animation-delay: 0.2s; }
.el-col:nth-child(3) { animation-delay: 0.3s; }
.el-col:nth-child(4) { animation-delay: 0.4s; }
.el-col:nth-child(5) { animation-delay: 0.5s; }

.model-stats {
  padding: 16px;
}

.model-stat-item {
  margin-bottom: 16px;
}

.model-name {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.el-progress {
  margin-top: 4px;
}

.el-progress-bar__inner {
  transition: all 0.3s ease;
}
</style>
