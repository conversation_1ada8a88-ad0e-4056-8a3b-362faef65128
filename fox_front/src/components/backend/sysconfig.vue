<template>
    <el-tooltip content="在这里配置系统的基本设置" placement="top">
        <el-alert title="系统配置" type="info" :closable="false"></el-alert>
    </el-tooltip>
    <el-form :model="form.system" label-width="auto" style="max-width: 500px;margin-top: 20px;">
        <el-form-item label="网站名称">
            <el-input v-model="form.system.systemName" placeholder="填写后将在前后台logo处显示"></el-input>
        </el-form-item>
        <el-form-item label="网站logo">
            <div class="logo-upload-container">
                <el-upload class="logo-uploader" :show-file-list="false" 
                    :before-upload="beforeLogoUpload" :http-request="customUpload" :headers="uploadHeaders">
                    <img v-if="form.system.systemLogo" :src="form.system.systemLogo" class="logo-preview" />
                    <el-icon v-else class="logo-uploader-icon">
                        <Plus />
                    </el-icon>
                </el-upload>
                <el-button v-if="form.system.systemLogo" type="danger" link @click="removeLogo">
                    <el-icon>
                        <Delete />
                    </el-icon>
                    删除Logo
                </el-button>
            </div>
        </el-form-item>
        <!-- 其他系统配置项 -->
        <el-form-item label="是否显示版本号">
            <el-switch v-model="form.system.showVersion" active-text="开启后，系统名称后面将显示系统版本号"></el-switch>
        </el-form-item>
        <el-form-item label="对话日志保存天数">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-input v-model="form.system.logRetentionDays" :min="1" placeholder="单位为天，0为永久保存"></el-input>
                </el-col>
                <el-col :span="8">
                    <el-text size="large" tag="mark">天</el-text>
                </el-col>
            </el-row>
        </el-form-item>
        <el-form-item label="是否开启站内购买">
            <el-switch v-model="form.system.enableBuy" active-text="开启后，用户可以在前台购买"></el-switch>
        </el-form-item>
        <el-form-item label="是否聊天界面显示用户信息">
            <el-switch v-model="form.system.enableUserInfo" ></el-switch>
        </el-form-item>
        <el-form-item label="是否开启使用说明">
            <el-switch v-model="form.system.enableUseNote" ></el-switch>
        </el-form-item>
        <el-form-item label="登录UI风格">
            <el-select v-model="form.system.uiStyle" placeholder="请选择登录UI风格">
                <el-option label="模仿官网" value="oaiLogin" />
                <el-option label="新UI风格" value="newLogin" />
            </el-select>
        </el-form-item>

        <el-divider />
        <el-form-item label="自定义加载JS脚本">
            <el-input v-model="form.system.scripts" type="textarea"
                placeholder="填写类似于<script src='https://example.com/script.js'></script>"></el-input>
        </el-form-item>

        <el-form-item label="4o模型共享限速模型">
            <el-select v-model="form.system.share4oModels" multiple filterable allow-create default-first-option
                placeholder="可多选或自定义输入模型名">
                <el-option v-for="item in form.system.share4oModels" :key="item" :label="item" :value="item" />
            </el-select>
        </el-form-item>

        <!-- ...继续添加其他配置项... -->
        <el-form-item style="justify-content: flex-end;">
            <div style="justify-content: flex-end;">
                <el-button type="primary" @click="submitSystemConfig">保存</el-button>
            </div>
        </el-form-item>
    </el-form>

    <!-- Logo预览对话框 -->
    <el-dialog v-model="logoPreviewVisible" title="Logo预览" width="30%">
        <div class="logo-preview-dialog">
            <img v-if="form.system.systemLogo" :src="form.system.systemLogo" class="logo-preview-image" />
            <el-empty v-else description="暂无Logo"></el-empty>
        </div>
    </el-dialog>
</template>

<script setup>
import { reactive, defineProps, ref } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import api from '@/axios'
import { ElMessage } from 'element-plus'

const logoPreviewVisible = ref(false)
const uploadHeaders = {
    // 如果需要认证token，在这里添加
}

// 自定义上传方法
const customUpload = async (options) => {
    try {
        const formData = new FormData()
        formData.append('file', options.file)
        
        console.log('开始上传文件:', options.file.name)
        
        const response = await api.post('/api/files/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                ...uploadHeaders
            },
            onUploadProgress: (progressEvent) => {
                if (options.onProgress) {
                    options.onProgress(progressEvent)
                }
            }
        })

        console.log('上传响应:', response)

        if (response.data.code === 0) {
            // 直接调用处理函数，而不是通过el-upload的on-success
            handleLogoSuccess(response.data)
        } else {
            options.onError(new Error(response.data.msg || '上传失败'))
        }
    } catch (error) {
        console.error('上传错误:', error)
        options.onError(error)
    }
}

const form = reactive({
    system: {
        systemName: '',
        systemLogo: '',
        showVersion: false,
        logRetentionDays: 30,
        enableUseNote: false,
        issuingCardSite: '',
        scripts: '',
        share4oModels: [],
        uiStyle: 'oaiLogin',
        enableBuy: false,
        enableUserInfo: false
    }
})

// Logo上传前的验证
const beforeLogoUpload = (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isImage) {
        ElMessage.error('Logo必须是图片格式!')
        return false
    }
    if (!isLt2M) {
        ElMessage.error('Logo大小不能超过 2MB!')
        return false
    }
    return true
}

// Logo上传成功的处理
const handleLogoSuccess = (data) => {
    console.log('处理上传成功:', data)
    if (!data || !data.data || !data.data.downloadUrl) {
        ElMessage.error('上传成功但返回数据格式不正确')
        console.error('无效的返回数据:', data)
        return
    }
    
    form.system.systemLogo = process.env.NODE_ENV === 'development' ? import.meta.env.VITE_API_HOST + data.data.downloadUrl : window.location.origin + data.data.downloadUrl
    ElMessage.success('Logo上传成功')
}

// 删除Logo
const removeLogo = () => {
    form.system.systemLogo = ''
    ElMessage.success('Logo已删除')
}

const submitSystemConfig = () => {
    const submitData = { ...form.system }
    if (Array.isArray(submitData.share4oModels)) {
        submitData.share4oModels = submitData.share4oModels.join(',')
    }
    api.post('/api/config/addOrUpdate', submitData).then(res => {
        ElMessage.success('保存成功')
    }).catch(err => {
        ElMessage.error('保存失败')
    })
}
const getSystemConfig = () => {
    api.post('/api/config/get', Object.keys(form.system)).then(res => {
        form.system = res.data.data
        form.system.showVersion = form.system.showVersion === 'true'
        form.system.enableUseNote = form.system.enableUseNote === 'true'
        form.system.enableBuy = form.system.enableBuy === 'true'
        form.system.enableUserInfo = form.system.enableUserInfo === 'true'
        if (!Array.isArray(form.system.share4oModels)) {
            form.system.share4oModels = form.system.share4oModels ? form.system.share4oModels.split(',') : []
        }
    }).catch(err => {
        ElMessage.error('获取系统配置失败')
    })
}
getSystemConfig()

</script>

<style scoped>
.logo-upload-container {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-uploader {
    width: 100px;
    height: 100px;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration);
}

.logo-uploader:hover {
    border-color: var(--el-color-primary);
}

.logo-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
}

.logo-preview {
    width: 100px;
    height: 100px;
    object-fit: contain;
}

.logo-preview-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.logo-preview-image {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
}
</style>