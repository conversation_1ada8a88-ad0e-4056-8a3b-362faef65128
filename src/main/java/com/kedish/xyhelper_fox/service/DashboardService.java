package com.kedish.xyhelper_fox.service;

import com.alibaba.fastjson.JSONObject;
import com.kedish.xyhelper_fox.component.ChatGptSessionAccessComponent;
import com.kedish.xyhelper_fox.repo.mapper.ChatGptUserMapper;
import com.kedish.xyhelper_fox.repo.mapper.OrderMapper;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.Order;
import com.kedish.xyhelper_fox.repo.model.UserGroup;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DashboardService {

    @Resource
    private ChatGptUserMapper chatGptUserMapper;

    @Resource
    private ChatgptUserService chatgptUserService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private UserGroupService userGroupService;

    @Resource
    private ChatGptSessionAccessComponent chatGptSessionAccessComponent;

    /**
     * 获取当天的模型和用户使用统计
     *
     * @return 使用统计数据
     */
    public JSONObject getTodayUsageStats() {
        JSONObject result = new JSONObject();
        String today = LocalDate.now().toString();

        // 获取模型使用统计
        Map<String, Integer> modelStats = chatGptSessionAccessComponent.getDailyModelStats(today);
        List<JSONObject> modelData = new ArrayList<>();

        int totalModelUsage = 0;
        for (Map.Entry<String, Integer> entry : modelStats.entrySet()) {
            JSONObject modelInfo = new JSONObject();
            modelInfo.put("model", entry.getKey());
            modelInfo.put("count", entry.getValue());
            modelData.add(modelInfo);
            totalModelUsage += entry.getValue();
        }

        // 按使用次数降序排序
        modelData.sort((a, b) -> b.getIntValue("count") - a.getIntValue("count"));

        // 获取用户使用统计
        Map<String, Integer> userStats = chatGptSessionAccessComponent.getDailyUserStats(today);

        // 计算活跃用户数（当天有使用的用户数）
        int activeUsers = userStats.size();

        // 计算总的使用次数
        int totalUsage = userStats.values().stream().mapToInt(Integer::intValue).sum();

        // 计算平均每用户使用次数
        double avgUsagePerUser = activeUsers > 0 ? (double) totalUsage / activeUsers : 0;

        // 组装结果
        result.put("modelStats", modelData);
        result.put("totalModelUsage", totalModelUsage);
        result.put("activeUsers", activeUsers);
        result.put("totalUsage", totalUsage);
        result.put("avgUsagePerUser", String.format("%.2f", avgUsagePerUser));
        result.put("onlineUserCount", getOnlineUserCount());

        return result;
    }

    public JSONObject getSummary() {
        JSONObject result = new JSONObject();

        result.put("totalUserCount", chatGptUserMapper.getTotalCount());
        //统计今天的新增用户数量
        result.put("todayNewUserCount", chatGptUserMapper.getNewCount(LocalDateTime.now().with(LocalTime.MIN), LocalDateTime.now().with(LocalTime.MAX)));

        List<Order> orders = orderMapper.queryOrderByCreateTime(LocalDateTime.now().with(LocalTime.MIN),
                LocalDateTime.now().with(LocalTime.MAX));

        result.put("todayOrderCount", orders.size());
        //今天未支付订单数量
        result.put("noPayTodayOrderCount",
                orders.stream()
                        .filter(e -> e.getStatus() == 0)
                        .count());

        //今日收入 - 计算今天已支付订单的总金额
        BigDecimal todayIncome = orders.stream()
                .filter(order -> order.getStatus() == 1) // 只统计已支付订单
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("todayIncome", todayIncome);

        return result;
    }

    /**
     * 获取用户分组分布数据
     *
     * @return 用户分组分布数据
     */
    public JSONObject getUserGroupDistribution() {
        JSONObject result = new JSONObject();

        // 获取所有用户组
        List<UserGroup> allGroups = userGroupService.getAllGroups();

        // 用于存储每个分组的用户数量
        List<JSONObject> groupData = new ArrayList<>();
        int totalUsers = 0;

        // 获取每个分组的用户数量
        for (UserGroup group : allGroups) {
            // 跳过已删除的分组
            if (group.getIsDeleted() != null && group.getIsDeleted()) {
                continue;
            }

            List<ChatgptUser> usersInGroup = chatgptUserService.getUsersByGroupId(group.getId());
            int userCount = usersInGroup.size();
            totalUsers += userCount;

            JSONObject groupInfo = new JSONObject();
            groupInfo.put("groupId", group.getId());
            groupInfo.put("groupName", group.getName());
            groupInfo.put("groupTitle", group.getTitle());
            groupInfo.put("userCount", userCount);

            groupData.add(groupInfo);
        }

        // 计算各分组用户占比
        if (totalUsers > 0) {
            for (JSONObject groupInfo : groupData) {
                int userCount = groupInfo.getIntValue("userCount");
                double percentage = ((double) userCount / totalUsers) * 100;
                groupInfo.put("percentage", String.format("%.2f", percentage) + "%");
            }
        }

        // 按用户数量降序排序
        groupData.sort((a, b) -> b.getIntValue("userCount") - a.getIntValue("userCount"));

        result.put("totalUsers", totalUsers);
        result.put("groupData", groupData);

        return result;
    }

    /**
     * 获取用户增长趋势数据 - 优化版（简化SQL）
     *
     * @param days 天数，默认7天
     * @return 用户增长趋势数据
     */
    public JSONObject getUserGrowthTrend(Integer days) {
        JSONObject result = new JSONObject();

        // 获取当前日期
        LocalDate today = LocalDate.now();
        List<JSONObject> dailyData = new ArrayList<>();
        List<JSONObject> totalUserTrend = new ArrayList<>();

        // 一次性获取截至今天的总用户数
        int currentTotalUsers = chatGptUserMapper.getTotalCount();

        // 一次性获取过去days天内每天的新增用户数
        LocalDateTime startDateTime = today.minusDays(days - 1).atStartOfDay();
        List<Map<String, Object>> dailyNewUsers = chatGptUserMapper.getDailyNewUsers(startDateTime);

        // 创建日期到新增用户数的映射
        Map<String, Integer> dateToNewUsersMap = new HashMap<>();
        for (Map<String, Object> entry : dailyNewUsers) {
            String date = entry.get("date").toString();
            Integer count = Integer.valueOf(entry.get("count").toString());
            dateToNewUsersMap.put(date, count);
        }

        // 计算每天的总用户数和新增用户数
        int runningTotal = currentTotalUsers;
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            String dateStr = date.toString();

            // 获取新增用户数
            int newUserCount = dateToNewUsersMap.getOrDefault(dateStr, 0);

            JSONObject dayData = new JSONObject();
            dayData.put("date", dateStr);
            dayData.put("newUsers", newUserCount);
            dailyData.add(dayData);

            // 计算该日期的总用户数（从当前总数反推）
            if (i < days - 1) {
                runningTotal -= newUserCount;
            }

            JSONObject totalDayData = new JSONObject();
            totalDayData.put("date", dateStr);
            totalDayData.put("totalUsers", runningTotal);
            totalUserTrend.add(0, totalDayData); // 插入到列表开头
        }

        result.put("dailyNewUsers", dailyData);
        result.put("totalUserTrend", totalUserTrend);

        // 计算用户增长率
        if (days > 1 && !totalUserTrend.isEmpty()) {
            JSONObject firstDay = totalUserTrend.get(0);
            JSONObject lastDay = totalUserTrend.get(totalUserTrend.size() - 1);
            int firstDayUsers = firstDay.getIntValue("totalUsers");
            int lastDayUsers = lastDay.getIntValue("totalUsers");

            double growthRate = 0;
            if (firstDayUsers > 0) {
                growthRate = ((double) (lastDayUsers - firstDayUsers) / firstDayUsers) * 100;
            }

            result.put("growthRate", String.format("%.2f", growthRate) + "%");
        }

        return result;
    }

    /**
     * 获取订单趋势数据 - 优化版
     *
     * @param days 天数，默认7天
     * @return 订单趋势数据
     */
    public JSONObject getOrderTrend(Integer days) {
        JSONObject result = new JSONObject();

        // 获取当前日期
        LocalDate today = LocalDate.now();
        List<JSONObject> dailyData = new ArrayList<>();

        // 计算开始日期和结束日期
        LocalDate startDate = today.minusDays(days - 1);
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = today.plusDays(1).atStartOfDay().minusNanos(1);

        // 一次性查询整个时间范围的订单
        List<Order> allOrders = orderMapper.queryOrderByTimeRange(startDateTime, endDateTime);

        // 按日期分组
        Map<LocalDate, List<Order>> ordersByDate = allOrders.stream()
                .collect(Collectors.groupingBy(order ->
                        order.getCreatedAt().toLocalDate()
                ));

        // 计算每天的数据
        for (int i = 0; i < days; i++) {
            LocalDate date = startDate.plusDays(i);
            List<Order> dateOrders = ordersByDate.getOrDefault(date, Collections.emptyList());

            // 计算订单总数
            int totalOrders = dateOrders.size();

            // 计算已支付订单数
            long paidOrders = dateOrders.stream()
                    .filter(order -> order.getStatus() == 1)
                    .count();

            // 计算订单总金额
            BigDecimal totalAmount = dateOrders.stream()
                    .filter(order -> order.getStatus() == 1)
                    .map(Order::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            JSONObject dayData = new JSONObject();
            dayData.put("date", date.toString());
            dayData.put("totalOrders", totalOrders);
            dayData.put("paidOrders", paidOrders);
            dayData.put("totalAmount", totalAmount);
            dailyData.add(dayData);
        }

        result.put("dailyData", dailyData);

        // 计算总计数据
        int totalOrders = allOrders.size();
        long totalPaidOrders = allOrders.stream()
                .filter(order -> order.getStatus() == 1)
                .count();

        BigDecimal totalAmount = allOrders.stream()
                .filter(order -> order.getStatus() == 1)
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        double paymentRate = totalOrders > 0 ? (double) totalPaidOrders / totalOrders * 100 : 0;

        result.put("totalOrders", totalOrders);
        result.put("totalPaidOrders", totalPaidOrders);
        result.put("totalAmount", totalAmount);
        result.put("paymentRate", String.format("%.2f", paymentRate) + "%");

        return result;
    }

    /**
     * 获取订单分布数据
     *
     * @param days 天数，默认7天
     * @return 订单分布数据
     */
    public JSONObject getOrderDistribution(Integer days) {
        JSONObject result = new JSONObject();

        // 计算时间范围
        LocalDate today = LocalDate.now();
        LocalDateTime startDateTime = today.minusDays(days - 1).atStartOfDay();
        LocalDateTime endDateTime = today.plusDays(1).atStartOfDay().minusNanos(1);

        // 获取指定时间范围内的已支付订单
        List<Order> paidOrders = orderMapper.queryPaidOrdersByTimeRange(startDateTime, endDateTime);

        // 按套餐分组统计
        Map<String, List<Order>> ordersBySalesPlan = paidOrders.stream()
                .collect(Collectors.groupingBy(Order::getSalesPlan));

        List<JSONObject> salesPlanData = new ArrayList<>();

        for (Map.Entry<String, List<Order>> entry : ordersBySalesPlan.entrySet()) {
            String salesPlan = entry.getKey();
            List<Order> orders = entry.getValue();

            int orderCount = orders.size();
            BigDecimal totalAmount = orders.stream()
                    .map(Order::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            JSONObject planData = new JSONObject();
            planData.put("salesPlan", salesPlan);
            planData.put("orderCount", orderCount);
            planData.put("totalAmount", totalAmount);

            salesPlanData.add(planData);
        }

        // 按订单数量降序排序
        salesPlanData.sort((a, b) -> b.getIntValue("orderCount") - a.getIntValue("orderCount"));

        result.put("salesPlanData", salesPlanData);

        // 计算总订单数和总金额
        int totalOrderCount = paidOrders.size();
        BigDecimal totalAmount = paidOrders.stream()
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        result.put("totalOrderCount", totalOrderCount);
        result.put("totalAmount", totalAmount);
        result.put("days", days);

        return result;
    }

    /**
     * 获取在线用户统计
     *
     * @return 在线用户数量
     */
    public long getOnlineUserCount() {
        return chatGptSessionAccessComponent.getOnlineUserCount();
    }
}
